"""
Teste para verificar se o erro de produção foi corrigido.
YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import numpy as np
import pandas as pd
from qualia.core.momentum_unified import calculate_unified_momentum, get_momentum_components


def test_production_scenario():
    """
    Simula o cenário de produção que estava causando o erro
    'name momentum_1m is not defined'
    """
    print("=== TESTE DE CENÁRIO DE PRODUÇÃO ===\n")
    
    # Simular dados de mercado como no binance_system
    np.random.seed(42)
    
    # Criar DataFrame simulado (como df no binance_system)
    dates = pd.date_range(start='2025-01-01', periods=50, freq='1min')
    base_price = 50000
    prices = []
    
    for i in range(50):
        change = np.random.normal(0, 0.01)  # 1% volatilidade
        base_price *= (1 + change)
        prices.append(base_price)
    
    df = pd.DataFrame({
        'close': prices,
        'volume': np.random.uniform(100, 1000, 50)
    }, index=dates)
    
    current_price = prices[-1] * 1.001  # Simular preço atual ligeiramente diferente
    
    print(f"Dados simulados:")
    print(f"  Períodos: {len(df)}")
    print(f"  Preço atual: {current_price:.2f}")
    print(f"  Últimos 5 preços: {prices[-5:]}")
    print()
    
    try:
        # Testar o método unificado (como agora está no binance_system)
        prices_array = df['close'].values
        momentum = calculate_unified_momentum(prices_array, current_price)
        components = get_momentum_components(prices_array, current_price)
        
        print("✅ MÉTODO UNIFICADO FUNCIONOU!")
        print(f"  Momentum final: {momentum:.6f}")
        print(f"  Componentes:")
        print(f"    1m:  {components['momentum_1m']:.6f}")
        print(f"    5m:  {components['momentum_5m']:.6f}")
        print(f"    15m: {components['momentum_15m']:.6f}")
        print(f"    Raw: {components['momentum_raw']:.6f}")
        print()
        
        # Testar se as variáveis estão acessíveis (como no código de coherence)
        momentum_1m = components['momentum_1m']
        momentum_5m = components['momentum_5m']
        
        # Simular o cálculo de coherence que estava falhando
        momentum_consistency = (1.0 if momentum_1m * momentum_5m > 0 else 0.3)
        
        print(f"✅ CÁLCULO DE COHERENCE FUNCIONOU!")
        print(f"  momentum_1m * momentum_5m = {momentum_1m * momentum_5m:.6f}")
        print(f"  Consistência: {momentum_consistency}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """Testa casos extremos que podem ocorrer em produção."""
    print("=== TESTE DE CASOS EXTREMOS ===\n")
    
    test_cases = [
        ("Poucos dados (2 pontos)", [100, 101]),
        ("Dados insuficientes para 5m", [100, 101, 102, 103]),
        ("Dados insuficientes para 15m", [100 + i for i in range(10)]),
        ("Preços constantes", [100] * 20),
        ("Volatilidade extrema", [100, 150, 80, 120, 60, 140, 70, 130, 90, 110]),
    ]
    
    all_passed = True
    
    for name, price_list in test_cases:
        try:
            prices = np.array(price_list)
            momentum = calculate_unified_momentum(prices)
            components = get_momentum_components(prices)
            
            # Testar acesso às variáveis
            momentum_1m = components['momentum_1m']
            momentum_5m = components['momentum_5m']
            
            print(f"✅ {name}: momentum={momentum:.6f}, 1m={momentum_1m:.6f}, 5m={momentum_5m:.6f}")
            
        except Exception as e:
            print(f"❌ {name}: ERRO - {e}")
            all_passed = False
    
    print()
    return all_passed


if __name__ == "__main__":
    print("TESTE DE CORREÇÃO DO ERRO DE PRODUÇÃO")
    print("=" * 50)
    print()
    
    test1 = test_production_scenario()
    test2 = test_edge_cases()
    
    print("=" * 50)
    if test1 and test2:
        print("🎉 CORREÇÃO VALIDADA! ERRO DE PRODUÇÃO RESOLVIDO!")
    else:
        print("⚠️ AINDA HÁ PROBLEMAS A RESOLVER!")
