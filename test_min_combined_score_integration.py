#!/usr/bin/env python3
"""
Script de teste para validar a integração do min_combined_score no sistema de calibração

FUNCIONALIDADES TESTADAS:
1. Carregamento do min_combined_score da configuração
2. Otimização do min_combined_score junto com outros thresholds
3. Aplicação do min_combined_score na validação de sinais
4. Salvamento do min_combined_score otimizado na configuração
"""

import asyncio
import logging
import sys
import os
import yaml
from datetime import datetime
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator, CalibrationPoint
from qualia.binance_system import QualiaBinanceCorrectedSystem

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_min_combined_score_integration():
    """Testa a integração completa do min_combined_score"""
    
    logger.info("=" * 80)
    logger.info(" TESTE DE INTEGRAÇÃO: min_combined_score")
    logger.info("=" * 80)
    
    try:
        # 1. Verificar se min_combined_score está na configuração
        logger.info("1. Verificando configuração atual...")
        config_path = Path('config/qualia_config.yaml')
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        current_min_score = config.get('signal_quality_filters', {}).get('min_combined_score')
        logger.info(f"   min_combined_score atual: {current_min_score}")
        
        # 2. Inicializar sistema de trading
        logger.info("2. Inicializando sistema de trading...")
        trading_system = QualiaBinanceCorrectedSystem()
        
        # 3. Verificar se o sistema carrega o min_combined_score corretamente
        logger.info("3. Testando carregamento do min_combined_score...")
        loaded_min_score = trading_system.config_manager.get('signal_quality_filters.min_combined_score')
        logger.info(f"   min_combined_score carregado: {loaded_min_score}")
        
        assert loaded_min_score == current_min_score, "Valor carregado não confere com configuração"
        logger.info("   ✓ Carregamento correto")
        
        # 4. Inicializar calibrador
        logger.info("4. Inicializando calibrador...")
        calibrator = QualiaMetricsCalibrator(trading_system)
        
        # 5. Testar funções de scoring
        logger.info("5. Testando funções de scoring...")
        
        # Criar ponto de teste
        test_point = CalibrationPoint(
            timestamp=datetime.now(),
            symbol='BTC/USDT',
            consciousness=0.85,
            coherence=0.90,
            confidence=0.75,
            volume_surge=1.2,
            momentum=0.05,
            future_return_1h=0.01,
            future_return_4h=0.015,
            was_profitable=True
        )
        
        # Testar cálculo de score
        test_thresholds = {
            'consciousness': 0.8,
            'coherence': 0.85,
            'confidence': 0.7,
            'volume_surge_min': 1.0,
            'momentum_min': 0.02,
            'min_combined_score': 0.65
        }
        
        combined_score = calibrator._calculate_flexible_score(test_point, test_thresholds)
        logger.info(f"   Score combinado calculado: {combined_score:.3f}")
        
        # 6. Testar aplicação de thresholds
        logger.info("6. Testando aplicação de thresholds...")
        
        test_points = [test_point]
        predictions = calibrator._apply_thresholds_to_points(test_points, test_thresholds)
        logger.info(f"   Predição com min_combined_score: {predictions[0]}")
        
        # Testar sem min_combined_score
        test_thresholds_no_score = {k: v for k, v in test_thresholds.items() if k != 'min_combined_score'}
        predictions_no_score = calibrator._apply_thresholds_to_points(test_points, test_thresholds_no_score)
        logger.info(f"   Predição sem min_combined_score: {predictions_no_score[0]}")
        
        # 7. Testar otimização (simulação)
        logger.info("7. Testando processo de otimização...")
        
        # Criar dados sintéticos para teste
        synthetic_points = []
        import numpy as np
        np.random.seed(42)
        
        for i in range(100):
            point = CalibrationPoint(
                timestamp=datetime.now(),
                symbol='TEST/USDT',
                consciousness=np.random.uniform(0.3, 0.98),
                coherence=np.random.uniform(0.4, 0.99),
                confidence=np.random.uniform(0.2, 0.95),
                volume_surge=np.random.uniform(0.5, 3.0),
                momentum=np.random.uniform(-0.3, 0.3),
                future_return_1h=np.random.normal(0, 0.02),
                future_return_4h=np.random.normal(0, 0.03),
                was_profitable=np.random.choice([True, False], p=[0.3, 0.7])
            )
            synthetic_points.append(point)
        
        # Simular análise de percentis
        percentile_analysis = {
            'consciousness': {'p10': 0.4, 'p15': 0.45, 'p20': 0.5, 'p25': 0.55, 'p50': 0.7, 'p90': 0.9},
            'coherence': {'p10': 0.5, 'p15': 0.55, 'p20': 0.6, 'p25': 0.65, 'p50': 0.8, 'p90': 0.95},
            'confidence': {'p10': 0.3, 'p15': 0.35, 'p20': 0.4, 'p25': 0.45, 'p50': 0.6, 'p90': 0.85},
            'volume_surge': {'p10': 0.6, 'p15': 0.7, 'p20': 0.8, 'p25': 0.9, 'p50': 1.2, 'p90': 2.5},
            'momentum': {'p10': 0.02, 'p15': 0.03, 'p20': 0.05, 'p25': 0.07, 'p50': 0.1, 'p90': 0.25}
        }
        
        # Testar otimização F1
        logger.info("   Executando otimização F1 com min_combined_score...")
        optimized_thresholds = calibrator._optimize_thresholds_f1(synthetic_points, percentile_analysis)
        
        logger.info("   Thresholds otimizados:")
        for key, value in optimized_thresholds.items():
            logger.info(f"     {key}: {value:.3f}")
        
        # Verificar se min_combined_score foi otimizado
        assert 'min_combined_score' in optimized_thresholds, "min_combined_score não foi incluído na otimização"
        logger.info("   ✓ min_combined_score incluído na otimização")
        
        # 8. Testar salvamento na configuração
        logger.info("8. Testando salvamento na configuração...")
        
        # Fazer backup da configuração atual
        backup_path = config_path.with_suffix('.yaml.test_backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        logger.info(f"   Backup criado: {backup_path}")
        
        try:
            # Testar atualização
            await calibrator._update_config_with_thresholds(optimized_thresholds)
            
            # Verificar se foi salvo corretamente
            with open(config_path, 'r', encoding='utf-8') as f:
                updated_config = yaml.safe_load(f)
            
            saved_min_score = updated_config.get('signal_quality_filters', {}).get('min_combined_score')
            expected_min_score = optimized_thresholds['min_combined_score']
            
            assert abs(saved_min_score - expected_min_score) < 0.001, "min_combined_score não foi salvo corretamente"
            logger.info(f"   ✓ min_combined_score salvo: {saved_min_score:.3f}")
            
        finally:
            # Restaurar configuração original
            with open(backup_path, 'r', encoding='utf-8') as f:
                original_config = yaml.safe_load(f)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(original_config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            backup_path.unlink()  # Remover backup
            logger.info("   Configuração original restaurada")
        
        logger.info("\n" + "=" * 80)
        logger.info(" ✅ TODOS OS TESTES PASSARAM!")
        logger.info("=" * 80)
        logger.info("FUNCIONALIDADES VALIDADAS:")
        logger.info("✓ Carregamento do min_combined_score da configuração")
        logger.info("✓ Cálculo do combined_score no calibrador")
        logger.info("✓ Aplicação do min_combined_score na validação")
        logger.info("✓ Otimização do min_combined_score junto com outros thresholds")
        logger.info("✓ Salvamento do min_combined_score otimizado na configuração")
        logger.info("\nO parâmetro min_combined_score está totalmente integrado!")
        
    except Exception as e:
        logger.error(f"❌ ERRO no teste: {e}")
        raise

async def main():
    """Função principal"""
    start_time = datetime.now()
    
    try:
        await test_min_combined_score_integration()
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Teste interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return 1
    
    finally:
        duration = datetime.now() - start_time
        logger.info(f"\n⏱️  Duração do teste: {duration}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
