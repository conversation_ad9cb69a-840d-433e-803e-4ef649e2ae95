"""
Teste simples de consistência do momentum unificado.
YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import numpy as np
from qualia.core.momentum_unified import calculate_unified_momentum, get_momentum_components
from qualia.strategies.metrics_adapter import QuantumMetricsAdapter


def test_consistency():
    """Testa consistência entre métodos."""
    print("=== TESTE DE CONSISTÊNCIA DO MOMENTUM ===\n")
    
    # Dados de teste
    prices = np.array([100, 101, 99, 102, 105, 103, 107, 110, 108, 112, 115, 113, 118, 120, 119, 122, 125])
    
    # Método unificado SEM ruído quântico (padrão)
    unified_momentum = calculate_unified_momentum(prices)

    # Método unificado COM ruído quântico (para comparar com adapter)
    unified_quantum = calculate_unified_momentum(prices, add_quantum_noise=True)

    # Método adapter (agora usa unificado com ruído quântico)
    adapter = QuantumMetricsAdapter()
    adapter_momentum = adapter.calculate_quantum_momentum(prices)
    
    # Componentes detalhados
    components = get_momentum_components(prices)
    
    print(f"Momentum Unificado (sem ruído): {unified_momentum:.6f}")
    print(f"Momentum Unificado (com ruído): {unified_quantum:.6f}")
    print(f"Momentum Adapter:               {adapter_momentum:.6f}")
    print(f"Diferença (unif vs adapter):    {abs(unified_quantum - adapter_momentum):.8f}")
    print()
    print("Componentes:")
    print(f"  1m:  {components['momentum_1m']:.6f}")
    print(f"  5m:  {components['momentum_5m']:.6f}")
    print(f"  15m: {components['momentum_15m']:.6f}")
    print(f"  Raw: {components['momentum_raw']:.6f}")
    print(f"  Norm: {components['momentum_normalized']:.6f}")
    print()
    
    # Verificar se estão no intervalo [-1, 1]
    in_range = -1.0 <= unified_momentum <= 1.0
    print(f"No intervalo [-1, 1]: {'✅' if in_range else '❌'}")
    
    # Verificar consistência (comparar versão com ruído quântico)
    consistent = abs(unified_quantum - adapter_momentum) < 1e-6
    print(f"Consistente: {'✅' if consistent else '❌'}")
    
    if consistent and in_range:
        print("\n🎉 TESTE PASSOU! MOMENTUM ESTÁ CONSISTENTE!")
        return True
    else:
        print("\n⚠️ TESTE FALHOU!")
        return False


def test_different_scenarios():
    """Testa diferentes cenários de mercado."""
    print("\n=== TESTE DE CENÁRIOS DIFERENTES ===\n")
    
    scenarios = [
        ("Tendência Alta", [100, 102, 104, 106, 108, 110, 112, 114, 116, 118, 120]),
        ("Tendência Baixa", [120, 118, 116, 114, 112, 110, 108, 106, 104, 102, 100]),
        ("Lateral", [100, 101, 99, 100, 102, 98, 101, 99, 100, 101, 99]),
        ("Volatilidade Alta", [100, 110, 90, 105, 85, 115, 95, 120, 80, 125, 75]),
    ]
    
    all_passed = True
    
    for name, price_list in scenarios:
        prices = np.array(price_list)
        momentum = calculate_unified_momentum(prices)
        in_range = -1.0 <= momentum <= 1.0
        
        print(f"{name:15}: {momentum:8.6f} {'✅' if in_range else '❌'}")
        
        if not in_range:
            all_passed = False
    
    print(f"\nTodos no intervalo: {'✅' if all_passed else '❌'}")
    return all_passed


if __name__ == "__main__":
    print("TESTE DE CONSISTÊNCIA DO MOMENTUM - SISTEMA QUALIA")
    print("=" * 55)
    
    test1 = test_consistency()
    test2 = test_different_scenarios()
    
    print("\n" + "=" * 55)
    if test1 and test2:
        print("🎉 TODOS OS TESTES PASSARAM!")
    else:
        print("⚠️ ALGUNS TESTES FALHARAM!")
