#!/usr/bin/env python3
"""
Script de teste para validar as correções no sistema de calibração QUALIA

CORREÇÕES TESTADAS:
1. Profit threshold: 2% → 0.8%
2. Triple-barrier labeling
3. Confidence dinâmico
4. Otimização por F1-score
5. Dados históricos expandidos
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
from qualia.binance_system import QualiaBinanceCorrectedSystem

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_calibration_improvements():
    """Testa as melhorias implementadas no sistema de calibração"""
    
    logger.info("=" * 80)
    logger.info(" TESTE DAS CORREÇÕES DO SISTEMA DE CALIBRAÇÃO QUALIA")
    logger.info("=" * 80)
    
    try:
        # 1. Inicializar sistema de trading
        logger.info("1. Inicializando sistema de trading...")
        trading_system = QualiaBinanceCorrectedSystem()
        
        # 2. Inicializar calibrador com correções
        logger.info("2. Inicializando calibrador com correções implementadas...")
        calibrator = QualiaMetricsCalibrator(trading_system)
        
        # 3. Testar com um subconjunto de ativos para validação rápida
        test_assets = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT', 'PEPE/USDT']
        
        logger.info(f"3. Testando calibração com {len(test_assets)} ativos...")
        logger.info(f"   Ativos de teste: {test_assets}")
        
        # 4. Executar calibração com parâmetros corrigidos
        logger.info("4. Executando calibração com correções:")
        logger.info("   ✓ Profit threshold: 0.8% (era 2%)")
        logger.info("   ✓ Triple-barrier labeling")
        logger.info("   ✓ Confidence dinâmico")
        logger.info("   ✓ Otimização por F1-score")
        logger.info("   ✓ 90 dias de dados históricos")
        
        results = {}
        
        for i, symbol in enumerate(test_assets, 1):
            logger.info(f"\n[{i}/{len(test_assets)}] Calibrando {symbol}...")
            
            try:
                result = await calibrator._calibrate_single_asset(
                    symbol=symbol,
                    days_back=90,  # Dados expandidos
                    profit_threshold=0.008,  # Threshold corrigido
                    time_horizon_hours=4
                )
                
                results[symbol] = result
                
                logger.info(f"   ✓ {symbol}: {result.success_rate:.1%} sucesso "
                          f"({result.profitable_points}/{result.total_points} pontos)")
                
                # Mostrar thresholds otimizados
                thresholds = result.recommended_thresholds
                logger.info(f"   Thresholds otimizados:")
                logger.info(f"     - Consciousness: {thresholds.get('consciousness', 0):.3f}")
                logger.info(f"     - Coherence: {thresholds.get('coherence', 0):.3f}")
                logger.info(f"     - Confidence: {thresholds.get('confidence', 0):.3f}")
                logger.info(f"     - Volume Surge: {thresholds.get('volume_surge_min', 0):.3f}")
                logger.info(f"     - Momentum: {thresholds.get('momentum_min', 0):.3f}")
                
            except Exception as e:
                logger.error(f"   ✗ Erro calibrando {symbol}: {e}")
                continue
        
        # 5. Análise dos resultados
        logger.info("\n" + "=" * 80)
        logger.info(" ANÁLISE DOS RESULTADOS")
        logger.info("=" * 80)
        
        if results:
            success_rates = [r.success_rate for r in results.values()]
            total_points = sum(r.total_points for r in results.values())
            total_profitable = sum(r.profitable_points for r in results.values())
            
            avg_success_rate = sum(success_rates) / len(success_rates)
            overall_success_rate = total_profitable / total_points if total_points > 0 else 0
            
            logger.info(f"Taxa de sucesso média: {avg_success_rate:.1%}")
            logger.info(f"Taxa de sucesso geral: {overall_success_rate:.1%}")
            logger.info(f"Total de pontos analisados: {total_points}")
            logger.info(f"Pontos lucrativos: {total_profitable}")
            
            # Comparação com baseline (22.4%)
            baseline_rate = 0.224
            improvement = avg_success_rate - baseline_rate
            
            logger.info(f"\nCOMPARAÇÃO COM BASELINE:")
            logger.info(f"Baseline anterior: {baseline_rate:.1%}")
            logger.info(f"Nova taxa média: {avg_success_rate:.1%}")
            logger.info(f"Melhoria: {improvement:+.1%} ({improvement/baseline_rate:+.1%} relativo)")
            
            if avg_success_rate > baseline_rate:
                logger.info("✅ SUCESSO: As correções melhoraram a taxa de sucesso!")
            else:
                logger.warning("⚠️  ATENÇÃO: Taxa ainda abaixo do esperado")
            
            # Top performers
            logger.info(f"\nTOP PERFORMERS:")
            sorted_results = sorted(results.items(), key=lambda x: x[1].success_rate, reverse=True)
            for symbol, result in sorted_results[:3]:
                logger.info(f"  {symbol}: {result.success_rate:.1%} "
                          f"({result.profitable_points}/{result.total_points})")
        
        else:
            logger.error("❌ FALHA: Nenhum resultado obtido")
            
    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO no teste: {e}")
        raise

async def main():
    """Função principal"""
    start_time = datetime.now()
    
    try:
        await test_calibration_improvements()
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Teste interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return 1
    
    finally:
        duration = datetime.now() - start_time
        logger.info(f"\n⏱️  Duração do teste: {duration}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
