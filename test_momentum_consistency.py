"""
Teste de consistência para validar que os cálculos de momentum
têm magnitudes consistentes entre calibração e produção.

YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import numpy as np
import pandas as pd
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.core.momentum_unified import calculate_unified_momentum, get_momentum_components
from qualia.strategies.metrics_adapter import QuantumMetricsAdapter


def generate_test_prices(n_points: int = 100, trend: float = 0.001, volatility: float = 0.02) -> np.ndarray:
    """
    Gera série de preços sintética para testes.
    
    Args:
        n_points: Número de pontos
        trend: Tendência por período
        volatility: Volatilidade
        
    Returns:
        Array de preços
    """
    np.random.seed(42)  # Para reprodutibilidade
    
    # Preço inicial
    price = 100.0
    prices = [price]
    
    for i in range(n_points - 1):
        # Movimento com tendência + ruído
        change = trend + np.random.normal(0, volatility)
        price *= (1 + change)
        prices.append(price)
    
    return np.array(prices)


def test_momentum_magnitude_consistency():
    """
    Testa se as magnitudes de momentum são consistentes
    entre diferentes implementações.
    """
    print("=== TESTE DE CONSISTÊNCIA DE MAGNITUDE DO MOMENTUM ===\n")
    
    # Gerar diferentes cenários de teste
    test_scenarios = [
        {"name": "Tendência Alta", "trend": 0.002, "volatility": 0.01},
        {"name": "Tendência Baixa", "trend": -0.002, "volatility": 0.01},
        {"name": "Lateral Baixa Vol", "trend": 0.0, "volatility": 0.005},
        {"name": "Lateral Alta Vol", "trend": 0.0, "volatility": 0.03},
        {"name": "Tendência Alta Vol", "trend": 0.001, "volatility": 0.025},
    ]
    
    adapter = QuantumMetricsAdapter()
    
    for scenario in test_scenarios:
        print(f"--- Cenário: {scenario['name']} ---")
        
        # Gerar preços para o cenário
        prices = generate_test_prices(
            n_points=50,
            trend=scenario['trend'],
            volatility=scenario['volatility']
        )
        
        # Calcular momentum usando método unificado
        unified_momentum = calculate_unified_momentum(prices)
        
        # Calcular momentum usando adapter (que agora usa método unificado)
        adapter_momentum = adapter.calculate_quantum_momentum(prices)
        
        # Obter componentes detalhados
        components = get_momentum_components(prices)
        
        # Verificar consistência
        diff = abs(unified_momentum - adapter_momentum)
        is_consistent = diff < 1e-6  # Tolerância muito pequena
        
        print(f"  Momentum Unificado: {unified_momentum:.6f}")
        print(f"  Momentum Adapter:   {adapter_momentum:.6f}")
        print(f"  Diferença:          {diff:.8f}")
        print(f"  Consistente:        {'✓' if is_consistent else '✗'}")
        print(f"  Componentes:")
        print(f"    1m:  {components['momentum_1m']:.6f}")
        print(f"    5m:  {components['momentum_5m']:.6f}")
        print(f"    15m: {components['momentum_15m']:.6f}")
        print(f"    Raw: {components['momentum_raw']:.6f}")
        print()
        
        if not is_consistent:
            print(f"  ⚠️  INCONSISTÊNCIA DETECTADA!")
            return False
    
    print("✅ TODOS OS CENÁRIOS PASSARAM NO TESTE DE CONSISTÊNCIA!")
    return True


def test_momentum_range_validation():
    """
    Testa se o momentum sempre fica no intervalo [-1, 1].
    """
    print("=== TESTE DE VALIDAÇÃO DE INTERVALO DO MOMENTUM ===\n")
    
    # Cenários extremos
    extreme_scenarios = [
        {"name": "Crash Extremo", "prices": [100, 95, 85, 70, 50, 30, 20, 15, 10, 5]},
        {"name": "Pump Extremo", "prices": [10, 15, 25, 40, 60, 85, 120, 160, 200, 250]},
        {"name": "Volatilidade Extrema", "prices": [100, 150, 80, 120, 60, 140, 70, 130, 90, 110]},
        {"name": "Preços Constantes", "prices": [100] * 20},
        {"name": "Mudança Mínima", "prices": [100 + i * 0.001 for i in range(20)]},
    ]
    
    all_valid = True
    
    for scenario in extreme_scenarios:
        print(f"--- Cenário: {scenario['name']} ---")
        
        prices = np.array(scenario['prices'])
        momentum = calculate_unified_momentum(prices)
        
        is_valid = -1.0 <= momentum <= 1.0
        
        print(f"  Momentum: {momentum:.6f}")
        print(f"  No intervalo [-1, 1]: {'✓' if is_valid else '✗'}")
        
        if not is_valid:
            print(f"  ⚠️  MOMENTUM FORA DO INTERVALO VÁLIDO!")
            all_valid = False
        
        print()
    
    if all_valid:
        print("✅ TODOS OS CENÁRIOS MANTIVERAM MOMENTUM NO INTERVALO VÁLIDO!")
    
    return all_valid


def test_momentum_components_consistency():
    """
    Testa se os componentes individuais do momentum são consistentes.
    """
    print("=== TESTE DE CONSISTÊNCIA DOS COMPONENTES ===\n")
    
    # Gerar série de preços com padrão conhecido
    prices = generate_test_prices(n_points=30, trend=0.001, volatility=0.01)
    
    # Obter componentes
    components = get_momentum_components(prices)
    
    # Calcular momentum manualmente para verificação
    current_price = prices[-1]
    manual_1m = (current_price - prices[-2]) / prices[-2] if len(prices) >= 2 else 0
    manual_5m = (current_price - prices[-6]) / prices[-6] if len(prices) >= 6 else 0
    manual_15m = (current_price - prices[-16]) / prices[-16] if len(prices) >= 16 else 0
    manual_raw = manual_1m * 0.5 + manual_5m * 0.3 + manual_15m * 0.2
    manual_normalized = np.tanh(manual_raw * 10)
    
    print("Comparação de Componentes:")
    print(f"  1m - Calculado: {components['momentum_1m']:.8f}, Manual: {manual_1m:.8f}")
    print(f"  5m - Calculado: {components['momentum_5m']:.8f}, Manual: {manual_5m:.8f}")
    print(f"  15m - Calculado: {components['momentum_15m']:.8f}, Manual: {manual_15m:.8f}")
    print(f"  Raw - Calculado: {components['momentum_raw']:.8f}, Manual: {manual_raw:.8f}")
    print(f"  Norm - Calculado: {components['momentum_normalized']:.8f}, Manual: {manual_normalized:.8f}")
    
    # Verificar consistência
    tolerances = {
        'momentum_1m': abs(components['momentum_1m'] - manual_1m) < 1e-10,
        'momentum_5m': abs(components['momentum_5m'] - manual_5m) < 1e-10,
        'momentum_15m': abs(components['momentum_15m'] - manual_15m) < 1e-10,
        'momentum_raw': abs(components['momentum_raw'] - manual_raw) < 1e-10,
        'momentum_normalized': abs(components['momentum_normalized'] - manual_normalized) < 1e-10,
    }
    
    all_consistent = all(tolerances.values())
    
    print(f"\nConsistência dos Componentes:")
    for component, is_consistent in tolerances.items():
        print(f"  {component}: {'✓' if is_consistent else '✗'}")
    
    if all_consistent:
        print("\n✅ TODOS OS COMPONENTES SÃO CONSISTENTES!")
    else:
        print("\n⚠️  INCONSISTÊNCIAS DETECTADAS NOS COMPONENTES!")
    
    return all_consistent


def main():
    """
    Executa todos os testes de consistência.
    """
    print("VALIDAÇÃO DE CONSISTÊNCIA DO MOMENTUM - SISTEMA QUALIA")
    print("=" * 60)
    print()
    
    tests = [
        ("Consistência de Magnitude", test_momentum_magnitude_consistency),
        ("Validação de Intervalo", test_momentum_range_validation),
        ("Consistência de Componentes", test_momentum_components_consistency),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"❌ ERRO NO TESTE {test_name}: {e}")
            results.append((test_name, False))
            print()
    
    # Resumo final
    print("=" * 60)
    print("RESUMO DOS TESTES:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSOU" if passed else "❌ FALHOU"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 TODOS OS TESTES PASSARAM! MOMENTUM ESTÁ CONSISTENTE!")
    else:
        print("⚠️  ALGUNS TESTES FALHARAM. REVISAR IMPLEMENTAÇÃO.")
    
    return all_passed


if __name__ == "__main__":
    main()
