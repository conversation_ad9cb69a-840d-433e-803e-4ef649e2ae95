#!/usr/bin/env python3
"""
Teste da efetividade dos novos thresholds
Simula diferentes cenários de mercado para verificar taxa de aprovação
"""

import numpy as np
import pandas as pd

def simulate_market_scenarios():
    """Simula diferentes cenários de mercado"""
    
    scenarios = []
    
    # Cenário 1: Mercado calmo (movimentos pequenos)
    for i in range(50):
        base_price = 100
        noise = np.random.normal(0, 0.001, 10)  # 0.1% de volatilidade
        prices = [base_price * (1 + sum(noise[:j+1])) for j in range(10)]
        scenarios.append(("calm", prices))
    
    # Cenário 2: Mercado normal (movimentos médios)
    for i in range(50):
        base_price = 100
        noise = np.random.normal(0, 0.005, 10)  # 0.5% de volatilidade
        prices = [base_price * (1 + sum(noise[:j+1])) for j in range(10)]
        scenarios.append(("normal", prices))
    
    # Cenário 3: Mercado volátil (movimentos grandes)
    for i in range(50):
        base_price = 100
        noise = np.random.normal(0, 0.02, 10)  # 2% de volatilidade
        prices = [base_price * (1 + sum(noise[:j+1])) for j in range(10)]
        scenarios.append(("volatile", prices))
    
    return scenarios

def calculate_quantum_metrics_mock(prices):
    """Mock das métricas quânticas para teste"""
    
    # Simular consciousness, coherence, confidence
    volatility = np.std(np.diff(prices) / prices[:-1])
    
    # Métricas baseadas na volatilidade (simulação)
    consciousness = max(0.3, min(0.95, 0.7 + np.random.normal(0, 0.1)))
    coherence = max(0.3, min(0.95, 0.8 - volatility * 5 + np.random.normal(0, 0.1)))
    confidence = max(0.3, min(0.95, 0.75 + np.random.normal(0, 0.1)))
    
    # Volume surge simulado
    volume_surge = max(0.5, min(3.0, 1.0 + np.random.normal(0, 0.3)))
    
    # YAA CORREÇÃO: Usar método unificado para consistência
    from src.qualia.core.momentum_unified import calculate_unified_momentum

    momentum = calculate_unified_momentum(prices)
    
    return {
        'consciousness': consciousness,
        'coherence': coherence,
        'confidence': confidence,
        'volume_surge': volume_surge,
        'momentum': momentum
    }

def test_threshold_effectiveness():
    """Testa a efetividade dos novos thresholds"""
    
    print("=== TESTE DE EFETIVIDADE DOS THRESHOLDS ===")
    
    # Thresholds otimizados para meta 15-20%
    thresholds = {
        'consciousness': 0.68,      # Ajuste fino para meta
        'coherence': 0.72,          # Ajuste fino para meta
        'confidence': 0.58,         # Ajuste fino para meta
        'volume_surge_min': 0.85,   # Ajuste fino para meta
        'momentum_min': 0.015       # Ajuste fino - momentum normalizado
    }
    
    print(f"Thresholds testados:")
    for key, value in thresholds.items():
        print(f"  {key}: {value:.3f}")
    
    # Simular cenários
    scenarios = simulate_market_scenarios()
    
    results_by_regime = {}
    
    for regime, prices in scenarios:
        if regime not in results_by_regime:
            results_by_regime[regime] = {'total': 0, 'passed': 0}
        
        # Calcular métricas
        metrics = calculate_quantum_metrics_mock(prices)
        
        # Aplicar thresholds
        passes = (
            metrics['consciousness'] >= thresholds['consciousness'] and
            metrics['coherence'] >= thresholds['coherence'] and
            metrics['confidence'] >= thresholds['confidence'] and
            metrics['volume_surge'] >= thresholds['volume_surge_min'] and
            abs(metrics['momentum']) >= thresholds['momentum_min']
        )
        
        results_by_regime[regime]['total'] += 1
        if passes:
            results_by_regime[regime]['passed'] += 1
    
    # Calcular estatísticas
    print(f"\n=== RESULTADOS POR REGIME ===")
    total_passed = 0
    total_scenarios = 0
    
    for regime, stats in results_by_regime.items():
        pass_rate = stats['passed'] / stats['total'] * 100
        total_passed += stats['passed']
        total_scenarios += stats['total']
        
        print(f"{regime.upper():10s}: {stats['passed']:2d}/{stats['total']:2d} ({pass_rate:5.1f}%)")
    
    overall_pass_rate = total_passed / total_scenarios * 100
    print(f"{'GERAL':10s}: {total_passed:2d}/{total_scenarios:2d} ({overall_pass_rate:5.1f}%)")
    
    # Avaliar resultado
    print(f"\n=== AVALIAÇÃO ===")
    target_min, target_max = 15, 20
    
    if target_min <= overall_pass_rate <= target_max:
        print(f"✅ SUCESSO: Taxa de {overall_pass_rate:.1f}% está na meta ({target_min}-{target_max}%)")
    elif overall_pass_rate < target_min:
        print(f"⚠️  BAIXA: Taxa de {overall_pass_rate:.1f}% abaixo da meta ({target_min}-{target_max}%)")
        print(f"   Sugestão: Relaxar thresholds")
    else:
        print(f"⚠️  ALTA: Taxa de {overall_pass_rate:.1f}% acima da meta ({target_min}-{target_max}%)")
        print(f"   Sugestão: Restringir thresholds")
    
    # Análise por métrica
    print(f"\n=== ANÁLISE POR MÉTRICA ===")
    metric_failures = {key: 0 for key in thresholds.keys()}
    
    for regime, prices in scenarios[:20]:  # Amostra menor para análise
        metrics = calculate_quantum_metrics_mock(prices)
        
        if metrics['consciousness'] < thresholds['consciousness']:
            metric_failures['consciousness'] += 1
        if metrics['coherence'] < thresholds['coherence']:
            metric_failures['coherence'] += 1
        if metrics['confidence'] < thresholds['confidence']:
            metric_failures['confidence'] += 1
        if metrics['volume_surge'] < thresholds['volume_surge_min']:
            metric_failures['volume_surge_min'] += 1
        if abs(metrics['momentum']) < thresholds['momentum_min']:
            metric_failures['momentum_min'] += 1
    
    print("Falhas por métrica (amostra de 20):")
    for metric, failures in metric_failures.items():
        failure_rate = failures / 20 * 100
        print(f"  {metric:20s}: {failures:2d}/20 ({failure_rate:5.1f}%)")

if __name__ == "__main__":
    np.random.seed(42)  # Para resultados reproduzíveis
    test_threshold_effectiveness()
